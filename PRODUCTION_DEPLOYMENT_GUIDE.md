# 🚀 Production Deployment Guide

## 📋 **Current Status**
✅ **Development HTTPS Server**: `production-https.js` is running and functional  
✅ **React App**: Built and served via HTTPS  
✅ **SSL Certificates**: Self-signed certificates working  
✅ **Mobile Access**: Camera-enabled QR scanning ready  

## 🔧 **Production Environment Commands**

### **Option 1: Single Server (Recommended for Small Scale)**

```bash
# 1. Build the React app for production
cd client
npm run build

# 2. Start the production HTTPS server (serves both frontend and API)
cd ../server
node production-https.js

# Access: https://YOUR_SERVER_IP:5444
```

### **Option 2: Separate Frontend/Backend (Recommended for Large Scale)**

```bash
# Terminal 1: Start Backend API Server
cd server
NODE_ENV=production node server.js

# Terminal 2: Serve Frontend (if using separate web server)
cd client
npm run build
# Then deploy build/ folder to web server (nginx, Apache, etc.)
```

### **Option 3: Production with Real SSL Certificates**

```bash
# 1. Get real SSL certificates (Let's Encrypt)
# Replace self-signed certificates in server/ssl/prod/

# 2. Update environment for production
cd server
cp .env .env.backup
# Edit .env:
# NODE_ENV=production
# ENABLE_HTTPS=true
# SSL_KEY_PATH=./ssl/prod/privkey.pem
# SSL_CERT_PATH=./ssl/prod/fullchain.pem

# 3. Start production server
node production-https.js
```

## 🌐 **Production Environment Variables**

Create `server/.env.production`:

```bash
# Production Environment
NODE_ENV=production
PORT=5000
HTTPS_PORT=443

# Database (Production)
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=hauling_qr_production
DB_USER=production_user
DB_PASSWORD=secure_production_password

# SSL Configuration
ENABLE_HTTPS=true
SSL_KEY_PATH=./ssl/prod/privkey.pem
SSL_CERT_PATH=./ssl/prod/fullchain.pem

# Security
JWT_SECRET=your-super-secure-jwt-secret-here
SESSION_SECRET=your-super-secure-session-secret-here

# Network
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## 🔒 **Production SSL Certificate Setup**

### **Option A: Let's Encrypt (Free, Recommended)**

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Certificates will be in:
# /etc/letsencrypt/live/yourdomain.com/privkey.pem
# /etc/letsencrypt/live/yourdomain.com/fullchain.pem

# Copy to your project
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem server/ssl/prod/
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem server/ssl/prod/
```

### **Option B: Commercial SSL Certificate**

```bash
# 1. Generate CSR (Certificate Signing Request)
cd server/ssl/prod
openssl req -new -newkey rsa:2048 -nodes -keyout privkey.pem -out domain.csr

# 2. Submit CSR to SSL provider (GoDaddy, Namecheap, etc.)
# 3. Download certificate files and place in server/ssl/prod/
```

## 🐳 **Docker Production Deployment**

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'
services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "443:5443"
      - "80:5000"
    environment:
      - NODE_ENV=production
      - ENABLE_HTTPS=true
    volumes:
      - ./server/ssl/prod:/app/server/ssl/prod:ro
      - ./data:/app/data
    restart: unless-stopped
    
  database:
    image: postgres:15
    environment:
      POSTGRES_DB: hauling_qr_production
      POSTGRES_USER: production_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

```bash
# Deploy with Docker
docker-compose -f docker-compose.prod.yml up -d
```

## ☁️ **Cloud Deployment Options**

### **Option A: VPS/Dedicated Server**

```bash
# 1. Clone repository on server
git clone https://github.com/yourusername/hauling-qr-trip-system.git
cd hauling-qr-trip-system

# 2. Install dependencies
cd client && npm install && npm run build
cd ../server && npm install

# 3. Setup SSL certificates (Let's Encrypt)
# 4. Configure environment variables
# 5. Start with PM2 (process manager)
npm install -g pm2
pm2 start production-https.js --name "hauling-qr-https"
pm2 startup
pm2 save
```

### **Option B: Heroku**

```bash
# 1. Install Heroku CLI
# 2. Create Heroku app
heroku create your-app-name

# 3. Set environment variables
heroku config:set NODE_ENV=production
heroku config:set ENABLE_HTTPS=true

# 4. Deploy
git push heroku main
```

### **Option C: AWS/DigitalOcean/Linode**

```bash
# 1. Create server instance
# 2. Install Node.js, PostgreSQL
# 3. Setup reverse proxy (nginx)
# 4. Configure SSL with Let's Encrypt
# 5. Deploy application
```

## 🔧 **Production Startup Scripts**

Create `server/start-production.sh`:

```bash
#!/bin/bash
echo "🚀 Starting Hauling QR Trip System - Production"

# Check if build exists
if [ ! -d "../client/build" ]; then
    echo "📦 Building React app..."
    cd ../client
    npm run build
    cd ../server
fi

# Start production server
echo "🔐 Starting HTTPS production server..."
NODE_ENV=production node production-https.js
```

Create `package.json` scripts:

```json
{
  "scripts": {
    "start": "node server.js",
    "start:https": "node production-https.js",
    "start:prod": "NODE_ENV=production node production-https.js",
    "build:client": "cd ../client && npm run build",
    "deploy": "npm run build:client && npm run start:prod"
  }
}
```

## 📱 **Production Access URLs**

```bash
# Local Development
https://localhost:5444

# Production Server
https://yourdomain.com          # Port 443 (standard HTTPS)
https://your-server-ip:5444     # Custom port

# Mobile Access
https://yourdomain.com          # For mobile QR scanning
```

## 🎯 **Quick Production Start Commands**

### **For Current Setup (Development/Testing):**

```bash
# Single command to start everything
cd server
node production-https.js

# Access: https://**************:5444
```

### **For True Production:**

```bash
# 1. Build frontend
cd client && npm run build

# 2. Start production server
cd ../server && NODE_ENV=production node production-https.js

# 3. Access via domain or IP
# https://yourdomain.com:5444
```

### **With Process Manager (Recommended):**

```bash
# Install PM2
npm install -g pm2

# Start with PM2
cd server
pm2 start production-https.js --name "hauling-qr-prod"

# Monitor
pm2 status
pm2 logs hauling-qr-prod

# Auto-restart on server reboot
pm2 startup
pm2 save
```

## 🔍 **Production Checklist**

- [ ] React app built (`npm run build`)
- [ ] SSL certificates configured
- [ ] Environment variables set
- [ ] Database configured
- [ ] Domain/DNS configured
- [ ] Firewall configured (ports 80, 443)
- [ ] Process manager setup (PM2)
- [ ] Monitoring setup
- [ ] Backup strategy
- [ ] SSL certificate auto-renewal

## 🎉 **Current Working Command**

**Right now, to start your production-ready HTTPS server:**

```bash
cd server
node production-https.js
```

**Access from mobile:** `https://**************:5443`

This serves both the React frontend and API backend via HTTPS with SSL encryption, enabling mobile camera access for QR scanning!
