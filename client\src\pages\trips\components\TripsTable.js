import React from 'react';

const TripsTable = ({ 
  trips, 
  loading, 
  pagination, 
  filters, 
  onPageChange, 
  onSort, 
  onViewTrip 
}) => {  // Enhanced status determination - CORRECTED: No auto-completed status display
  const getEnhancedStatus = (status, trip) => {
    // CRITICAL: Always preserve original status values as per requirements
    // Never create new status values like 'auto_completed'
    return { status, label: null };
  };

  // Get status badge for trip status
  const getStatusBadge = (status, trip) => {
    const wasException = trip && trip.is_exception && trip.exception_approved_at;

    // Get enhanced status for better accuracy
    const enhancedStatus = getEnhancedStatus(status, trip);
    const displayStatus = enhancedStatus.status;
    const customLabel = enhancedStatus.label;

    const statusStyles = {
      'assigned': 'bg-blue-100 text-blue-800',
      'loading_start': wasException ? 'bg-warning-100 text-warning-800' : 'bg-yellow-100 text-yellow-800',
      'loading_end': wasException ? 'bg-warning-100 text-warning-800' : 'bg-green-100 text-green-800',
      'unloading_start': wasException ? 'bg-warning-100 text-warning-800' : 'bg-orange-100 text-orange-800',
      'unloading_end': wasException ? 'bg-warning-100 text-warning-800' : 'bg-purple-100 text-purple-800',
      'trip_completed': wasException ? 'bg-warning-success-50 text-success-800 border border-warning-300' : 'bg-success-100 text-success-800',
      'exception_pending': 'bg-danger-100 text-danger-800',
      'cancelled': 'bg-secondary-100 text-secondary-800'
    };

    const statusLabels = {
      'assigned': 'Assigned',
      'loading_start': wasException ? 'Loading Started (Approved Exception)' : 'Loading Started',
      'loading_end': wasException ? 'Loading Complete (Approved Exception)' : 'Loading Complete',
      'unloading_start': wasException ? 'Unloading Started (Approved Exception)' : 'Unloading Started',
      'unloading_end': wasException ? 'Unloading Complete (Approved Exception)' : 'Unloading Complete',
      'trip_completed': wasException ? 'Completed (Approved Exception)' : 'Completed',
      'exception_pending': 'Exception Pending',
      'cancelled': 'Cancelled'
    };

    const statusIcons = {
      'assigned': '📋',
      'loading_start': wasException ? '🔄⬆️' : '⬆️',
      'loading_end': wasException ? '🔄✅' : '✅',
      'unloading_start': wasException ? '🔄⬇️' : '⬇️',
      'unloading_end': wasException ? '🔄✅' : '✅',
      'trip_completed': wasException ? '🔄🏁' : '🏁',
      'exception_pending': '⚠️',
      'cancelled': '❌'
    };

    // For trips with exception_pending, show the status badge without the trip parameter
    if (status === 'exception_pending' || !trip) {
      return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status] || 'bg-secondary-100 text-secondary-800'}`}>
          <span className="mr-1">{statusIcons[status] || '📊'}</span>
          {statusLabels[status] || status}
        </span>
      );
    }

    // Use custom label if available, otherwise use default label
    const displayLabel = customLabel || statusLabels[displayStatus] || displayStatus;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[displayStatus] || 'bg-secondary-100 text-secondary-800'}`}>
        <span className="mr-1">{statusIcons[displayStatus] || '📊'}</span>
        {displayLabel}
      </span>
    );
  };

  // Format duration in minutes to human readable
  const formatDuration = (minutes) => {
    if (!minutes || minutes === 0) return '-';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Calculate precise durations for a trip with cross-trip analysis for Travel_2
  const calculateTripDurations = (trip) => {
    const getMinutesDifference = (startTime, endTime) => {
      if (!startTime || !endTime) return 0;
      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end - start;
      return Math.max(0, Math.round(diffMs / (1000 * 60)));
    };

    // Calculate individual duration components
    const loadingDuration = trip.loading_duration_minutes ||
      getMinutesDifference(trip.loading_start_time, trip.loading_end_time);

    const unloadingDuration = trip.unloading_duration_minutes ||
      getMinutesDifference(trip.unloading_start_time, trip.unloading_end_time);

    // DEBUG: Log the trip data for analysis
    console.log('=== DURATION CALCULATION DEBUG ===');
    console.log('Trip ID:', trip.id);
    console.log('Total Duration (from DB):', trip.total_duration_minutes);
    console.log('Loading Duration:', loadingDuration);
    console.log('Unloading Duration:', unloadingDuration);
    console.log('Travel Duration (from DB):', trip.travel_duration_minutes);
    console.log('Travel_1 Minutes (from DB):', trip.travel_1_minutes);
    console.log('Travel_2 Minutes (from DB):', trip.travel_2_minutes);
    console.log('unloading_to_loading_travel_minutes (from DB):', trip.unloading_to_loading_travel_minutes);
    console.log('Timestamps:');
    console.log('  loading_start_time:', trip.loading_start_time);
    console.log('  loading_end_time:', trip.loading_end_time);
    console.log('  unloading_start_time:', trip.unloading_start_time);
    console.log('  unloading_end_time:', trip.unloading_end_time);
    console.log('  trip_completed_time:', trip.trip_completed_time);

    // FORWARD TRAVEL: From Loading end to Unloading start
    let forwardTravel = 0;
    
    // Priority 1: Direct timestamp calculation (most accurate)
    if (trip.loading_end_time && trip.unloading_start_time) {
      forwardTravel = getMinutesDifference(trip.loading_end_time, trip.unloading_start_time);
      console.log('Forward calculated from timestamps:', forwardTravel);
    }
    // Priority 2: Server-provided travel_1_minutes
    else if (trip.travel_1_minutes) {
      forwardTravel = trip.travel_1_minutes;
      console.log('Forward from travel_1_minutes:', forwardTravel);
    }
    // Priority 3: Use total travel_duration_minutes (assuming it represents forward travel only)
    else if (trip.travel_duration_minutes) {
      forwardTravel = trip.travel_duration_minutes;
      console.log('Forward from travel_duration_minutes:', forwardTravel);
    }
    // Priority 4: Calculate from total - load - unload - return (if return is available)
    else if (trip.total_duration_minutes && loadingDuration >= 0 && unloadingDuration >= 0) {
      // First check if we have any return travel data
      let knownReturnTravel = 0;
      if (trip.travel_2_minutes) {
        knownReturnTravel = trip.travel_2_minutes;
      } else if (trip.unloading_to_loading_travel_minutes) {
        knownReturnTravel = trip.unloading_to_loading_travel_minutes;
      }
      
      if (knownReturnTravel > 0) {
        // Forward = Total - Load - Unload - Return
        forwardTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - unloadingDuration - knownReturnTravel);
        console.log('Forward calculated from total minus load/unload/return:', forwardTravel);
      } else {
        // No return data available, assume Forward = Total - Load - Unload (single direction trip)
        forwardTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - unloadingDuration);
        console.log('Forward calculated from total minus load/unload (no return data):', forwardTravel);
      }
    }

    // RETURN TRAVEL: From Unloading end to Trip completion (unloading_end_time → trip_completed_time)
    let returnTravel = 0;
    
    // Priority 1: CORRECT CALCULATION FIRST - unloading_end_time to trip_completed_time
    if (trip.unloading_end_time && trip.trip_completed_time) {
      returnTravel = getMinutesDifference(trip.unloading_end_time, trip.trip_completed_time);
      console.log('Return calculated from unloading_end_time to trip_completed_time:', returnTravel);
      console.log('Timestamps: unloading_end_time:', trip.unloading_end_time, '→ trip_completed_time:', trip.trip_completed_time);
    }
    // Priority 2: Server-provided travel_2_minutes
    else if (trip.travel_2_minutes) {
      returnTravel = trip.travel_2_minutes;
      console.log('Return from travel_2_minutes:', returnTravel);
    }
    // Priority 3: Server-provided unloading_to_loading_travel_minutes (potentially incorrect)
    else if (trip.unloading_to_loading_travel_minutes) {
      returnTravel = trip.unloading_to_loading_travel_minutes;
      console.log('Return from unloading_to_loading_travel_minutes (old method):', returnTravel);
    }
    // Priority 4: Fallback - Calculate from total duration
    else if (trip.total_duration_minutes && loadingDuration > 0 && unloadingDuration > 0 && forwardTravel > 0) {
      returnTravel = Math.max(0, trip.total_duration_minutes - loadingDuration - forwardTravel - unloadingDuration);
      console.log('Return calculated from total minus other components:', returnTravel);
    }
    else {
      if (!trip.unloading_end_time) {
        console.log('Cannot calculate return travel - unloading_end_time is missing');
      } else if (!trip.trip_completed_time) {
        console.log('Cannot calculate return travel - trip_completed_time is missing');
      } else {
        console.log('Cannot calculate return travel - insufficient data');
      }
    }

    // TOTAL DURATION calculation - Always calculate from components for accuracy
    let totalDuration = 0;
    
    // Priority 1: Calculate from all components (most accurate with corrected values)
    if (loadingDuration >= 0 && forwardTravel >= 0 && unloadingDuration >= 0 && returnTravel >= 0) {
      totalDuration = loadingDuration + forwardTravel + unloadingDuration + returnTravel;
      console.log('Total calculated from components:', totalDuration);
    }
    // Priority 2: Direct timestamp calculation
    else if (trip.loading_start_time && trip.trip_completed_time) {
      totalDuration = getMinutesDifference(trip.loading_start_time, trip.trip_completed_time);
      console.log('Total calculated from loading_start_time to trip_completed_time:', totalDuration);
    }
    // Priority 3: Alternative timestamp calculation
    else if (trip.loading_start_time && trip.unloading_end_time) {
      totalDuration = getMinutesDifference(trip.loading_start_time, trip.unloading_end_time);
      console.log('Total calculated from loading_start_time to unloading_end_time:', totalDuration);
    }
    // Priority 4: Fallback to database value
    else {
      totalDuration = trip.total_duration_minutes || 0;
      console.log('Total from database fallback:', totalDuration);
    }

    console.log('FINAL CALCULATED VALUES:');
    console.log('Total:', totalDuration);
    console.log('Load:', loadingDuration);
    console.log('Forward:', forwardTravel);
    console.log('Unload:', unloadingDuration);
    console.log('Return:', returnTravel);
    console.log('Sum check:', loadingDuration + forwardTravel + unloadingDuration + returnTravel);
    console.log('=====================================');

    return {
      total: totalDuration,
      load: loadingDuration,
      travel1: forwardTravel,
      unload: unloadingDuration,
      travel2: returnTravel
    };
  };

  // Format datetime for display
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate trip progress percentage
  const getTripProgress = (trip) => {
    const statusOrder = [
      'assigned',
      'loading_start',
      'loading_end',
      'unloading_start',
      'unloading_end',
      'trip_completed'
    ];
    
    const currentIndex = statusOrder.indexOf(trip.status);
    if (currentIndex === -1) return 0;
    
    return Math.round((currentIndex / (statusOrder.length - 1)) * 100);
  };

  // Calculate display trip number (truck-based) for UI consistency
  // This ensures unique trip numbers per truck per day in the interface
  const getDisplayTripNumber = (trip, allTrips) => {
    // Group trips by truck and date
    const truckTrips = allTrips
      .filter(t =>
        t.truck_number === trip.truck_number &&
        new Date(t.created_at).toDateString() === new Date(trip.created_at).toDateString()
      )
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Find the index of current trip + 1 for display
    const displayNumber = truckTrips.findIndex(t => t.id === trip.id) + 1;
    return displayNumber;
  };

  // Sortable header component
  const SortableHeader = ({ column, children }) => {
    const isSorted = filters.sortBy === column;
    const isAsc = filters.sortOrder === 'asc';

    return (
      <th 
        scope="col" 
        className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider cursor-pointer hover:bg-secondary-50 select-none"
        onClick={() => onSort(column)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          <div className="flex flex-col">
            <svg 
              className={`w-3 h-3 ${isSorted && isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            <svg 
              className={`w-3 h-3 -mt-1 ${isSorted && !isAsc ? 'text-primary-600' : 'text-secondary-400'}`} 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </th>
    );
  };

  // Pagination component
  const Pagination = () => {
    if (pagination.totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pages = [];
      const { currentPage, totalPages } = pagination;
      const maxVisible = 5;

      if (totalPages <= maxVisible) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        }
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-secondary-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
            className="relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-300 text-sm font-medium rounded-md text-secondary-700 bg-white hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-secondary-700">
              Showing{' '}
              <span className="font-medium">
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{pagination.totalItems}</span>{' '}
              results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Previous</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </button>
              
              {getPageNumbers().map((page, index) => (
                page === '...' ? (
                  <span key={index} className="relative inline-flex items-center px-4 py-2 border border-secondary-300 bg-white text-sm font-medium text-secondary-700">
                    ...
                  </span>
                ) : (
                  <button
                    key={index}
                    onClick={() => onPageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      page === pagination.currentPage
                        ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                        : 'bg-white border-secondary-300 text-secondary-500 hover:bg-secondary-50'
                    }`}
                  >
                    {page}
                  </button>
                )
              ))}
              
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="sr-only">Next</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex space-x-4">
                <div className="h-4 bg-secondary-200 rounded w-1/8"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/5"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/6"></div>
                <div className="h-4 bg-secondary-200 rounded w-1/8"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Enhanced route rendering with multi-location workflow support
  const renderEnhancedRoute = (trip) => {
    // Check for multi-location workflow
    // eslint-disable-next-line no-unused-vars
    const isExtendedTrip = trip.is_extended_trip || false;
    // eslint-disable-next-line no-unused-vars
    const workflowType = trip.workflow_type || 'standard';

    // Safe parsing of location_sequence
    let locationSequence = null;
    if (trip.location_sequence) {
      try {
        locationSequence = typeof trip.location_sequence === 'string'
          ? JSON.parse(trip.location_sequence)
          : trip.location_sequence;
      } catch (e) {
        console.warn('Failed to parse location_sequence:', e);
        locationSequence = null;
      }
    }
    // eslint-disable-next-line no-unused-vars
    const _locationSequence = locationSequence; // Keep for future multi-location route implementation

    // Safe parsing of assignment_notes
    let isDynamicAssignment = false;
    if (trip.assignment_notes) {
      try {
        const notes = typeof trip.assignment_notes === 'string'
          ? JSON.parse(trip.assignment_notes)
          : trip.assignment_notes;
        isDynamicAssignment = notes.creation_method === 'dynamic_assignment';
      } catch (e) {
        console.warn('Failed to parse assignment_notes:', e);
        isDynamicAssignment = false;
      }
    }

    // CORRECTED FIX: Determine if dynamic route discovery is actively in progress
    const isActiveDiscovery = isDynamicAssignment && (() => {
      // CORRECTED LOGIC: Check if assignment exists in Assignment Management with established locations
      const hasEstablishedLoadingLocation = trip.loading_location_id !== null && trip.loading_location_id !== undefined;
      const hasEstablishedUnloadingLocation = trip.unloading_location_id !== null && trip.unloading_location_id !== undefined;
      const existsInAssignmentManagement = hasEstablishedLoadingLocation && hasEstablishedUnloadingLocation;

      // If assignment exists in Assignment Management, NEVER show Dynamic Route
      if (existsInAssignmentManagement) {
        return false;
      }

      // Only show Dynamic Route for genuinely new assignments without established locations
      return true;
    })();

    // FIXED: Always use traditional route display with proper location names from main query
    // The location_sequence may contain outdated "Unknown Loading/Unloading" values from migrations
    // The main query provides properly formatted location names with fallbacks
    return renderTraditionalRoute(trip, isDynamicAssignment, isActiveDiscovery);
  };

  // Render multi-location route with workflow indicators
  // eslint-disable-next-line no-unused-vars
  const renderMultiLocationRoute = (trip, locationSequence, workflowType, isExtendedTrip) => {
    return (
      <div className="text-sm space-y-1">
        {locationSequence.map((location, index) => (
          <div key={index}>
            <div className="flex items-center">
              <span className="mr-1">
                {location.confirmed ? '📍' : '❓'}
              </span>
              <span className="truncate">{location.name}</span>
              {location.type === 'loading' && (
                <span className="ml-1 text-green-600">⬆️</span>
              )}
              {location.type === 'unloading' && (
                <span className="ml-1 text-red-600">⬇️</span>
              )}
            </div>
            {index < locationSequence.length - 1 && (
              <div className="flex items-center justify-center">
                <span className="text-secondary-400 text-xs">↓</span>
              </div>
            )}
          </div>
        ))}

        {/* Workflow indicator */}
        {isExtendedTrip && (
          <div className="flex items-center mt-1">
            <span className="text-blue-500 text-xs mr-1">🔄</span>
            <span className="text-xs text-blue-600 font-medium">
              {getWorkflowLabel(workflowType, trip.cycle_number)}
            </span>
          </div>
        )}
      </div>
    );
  };

  // Get workflow label based on type and cycle number
  const getWorkflowLabel = (workflowType, cycleNumber) => {
    switch (workflowType) {
      case 'extended':
        return 'Extended Trip';
      case 'cycle':
        return `Cycle Trip #${cycleNumber || 1}`;
      case 'dynamic':
        return 'Dynamic Route';
      default:
        return null;
    }
  };

  // Get workflow indicator for Assignment & Driver column
  const getWorkflowIndicator = (trip) => {
    // Check if this trip is part of an extended workflow (either as extended trip or baseline)
    if (trip.is_extended_trip) {
      switch (trip.workflow_type) {
        case 'extended':
          return '🔄 Extended Trip';
        case 'cycle':
          return `🔄 Cycle Trip #${trip.cycle_number || 1}`;
        case 'dynamic':
          return '🔄 Dynamic Route';
        default:
          return null;
      }
    }

    // Check if this is a baseline trip that was extended (check notes)
    if (trip.notes && trip.status === 'trip_completed') {
      try {
        const notes = typeof trip.notes === 'string' ? JSON.parse(trip.notes) : trip.notes;
        if (notes.extended_workflow) {
          return '🔄 Extended to Next Location';
        }
        if (notes.cycle_workflow) {
          return '🔄 Continued as Cycle';
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return null;
  };

  // TRIP FLOW LOGIC: renderDynamicRoute function as specified in requirements
  // Displays uncertainty indicators for dynamic route discovery
  // eslint-disable-next-line no-unused-vars
  const renderDynamicRoute = (trip) => {
    // Check if this is a dynamic assignment
    let isDynamicAssignment = false;
    if (trip.assignment_notes) {
      try {
        const notes = typeof trip.assignment_notes === 'string'
          ? JSON.parse(trip.assignment_notes)
          : trip.assignment_notes;
        isDynamicAssignment = notes.creation_method === 'dynamic_assignment';
      } catch (e) {
        isDynamicAssignment = false;
      }
    }

    // Determine if dynamic route discovery is actively in progress
    const isActiveDiscovery = isDynamicAssignment && (() => {
      const hasEstablishedLoadingLocation = trip.loading_location_id !== null && trip.loading_location_id !== undefined;
      const hasEstablishedUnloadingLocation = trip.unloading_location_id !== null && trip.unloading_location_id !== undefined;
      const existsInAssignmentManagement = hasEstablishedLoadingLocation && hasEstablishedUnloadingLocation;

      if (existsInAssignmentManagement) {
        return false;
      }

      return true;
    })();

    return renderTraditionalRoute(trip, isDynamicAssignment, isActiveDiscovery);
  };

  // Traditional route rendering (existing logic)
  const renderTraditionalRoute = (trip, isDynamicAssignment, isActiveDiscovery) => {
    // Determine route certainty based on trip status and dynamic assignment
    const getLocationCertainty = (locationType) => {
      if (!isDynamicAssignment) {
        return 'confirmed'; // Traditional assignments are always confirmed
      }

      // For dynamic assignments, check if location has been physically visited
      if (locationType === 'loading') {
        // Loading location is confirmed if trip has started loading or beyond
        return ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status)
          ? 'confirmed' : 'predicted';
      } else {
        // Unloading location is confirmed if truck has reached unloading phase
        return ['unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status)
          ? 'confirmed' : 'predicted';
      }
    };

    const loadingCertainty = getLocationCertainty('loading');
    const unloadingCertainty = getLocationCertainty('unloading');

    // Get appropriate icons and styling based on certainty
    const getLocationDisplay = (location, certainty, type) => {
      const isLoading = type === 'loading';
      const baseColor = isLoading ? 'text-green-600' : 'text-red-600';
      const uncertainColor = isLoading ? 'text-green-400' : 'text-red-400';

      // Handle missing location names properly
      let displayText = location;
      if (!location || location === 'Unknown Loading' || location === 'Unknown Unloading' ||
          location.startsWith('No ') || location.startsWith('Location ID: ')) {
        if (isDynamicAssignment) {
          displayText = 'To be determined';
        } else {
          // Show the original text if it's a location ID reference, otherwise show missing
          if (location && location.startsWith('Location ID: ')) {
            displayText = location; // Keep the "Location ID: X" format for debugging
          } else if (location && location.startsWith('No ')) {
            displayText = location; // Keep "No Loading Location" format
          } else {
            displayText = `Missing ${isLoading ? 'Loading' : 'Unloading'} Location`;
          }
        }
      }

      if (certainty === 'confirmed') {
        return {
          icon: '📍',
          color: baseColor,
          text: displayText,
          suffix: ''
        };
      } else {
        return {
          icon: '❓',
          color: uncertainColor,
          text: displayText,
          suffix: ' (predicted)'
        };
      }
    };

    const loadingDisplay = getLocationDisplay(trip.loading_location_name, loadingCertainty, 'loading');
    const unloadingDisplay = getLocationDisplay(trip.unloading_location_name, unloadingCertainty, 'unloading');

    // TRIP FLOW LOGIC: Implement horizontal format as specified
    // Format: ⬆️ (Loading Location) → ⬇️ (Unloading Location)
    return (
      <div className="text-sm text-secondary-900">
        <div className="flex items-center space-x-2">
          {/* Loading Location with ⬆️ indicator */}
          <div className="flex items-center">
            <span className={`mr-1 flex-shrink-0 ${loadingDisplay.color}`}>
              {loadingDisplay.icon}
            </span>
            <span className="truncate">
              {loadingDisplay.text}
              {loadingDisplay.suffix && (
                <span className="text-xs text-secondary-500 ml-1">
                  {loadingDisplay.suffix}
                </span>
              )}
            </span>
            <span className="ml-1 text-green-600">⬆️</span>
          </div>

          {/* Horizontal Route Arrow */}
          <span className="text-secondary-400 text-sm">→</span>

          {/* Unloading Location with ⬇️ indicator */}
          <div className="flex items-center">
            <span className={`mr-1 flex-shrink-0 ${unloadingDisplay.color}`}>
              {unloadingDisplay.icon}
            </span>
            <span className="truncate">
              {unloadingDisplay.text}
              {unloadingDisplay.suffix && (
                <span className="text-xs text-secondary-500 ml-1">
                  {unloadingDisplay.suffix}
                </span>
              )}
            </span>
            <span className="ml-1 text-red-600">⬇️</span>
          </div>
        </div>

        {/* Dynamic Assignment Indicator - FIXED: Only show during active discovery */}
        {isActiveDiscovery && (
          <div className="flex items-center mt-1">
            <span className="text-blue-500 text-xs mr-1">🔄</span>
            <span className="text-xs text-blue-600 font-medium">Dynamic Route</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white shadow-lg overflow-hidden sm:rounded-lg border border-secondary-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-secondary-200">
          <thead className="bg-gradient-to-r from-secondary-50 to-secondary-100">
            <tr>
              <SortableHeader column="trip_number">Trip #</SortableHeader>
              <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                <span className="hidden lg:inline">Assignment & </span>Driver
              </th>
              <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Route
              </th>
              <SortableHeader column="status">Status</SortableHeader>
              <th scope="col" className="hidden md:table-cell px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Progress
              </th>
              <th scope="col" className="hidden lg:table-cell px-4 sm:px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Duration
              </th>
              <SortableHeader column="created_at">
                <span className="hidden sm:inline">Date </span>Started
              </SortableHeader>
              <th scope="col" className="px-4 sm:px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-secondary-200">
            {trips.length === 0 ? (
              <tr>
                <td colSpan="8" className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <svg className="w-12 h-12 text-secondary-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                    </svg>
                    <h3 className="text-lg font-medium text-secondary-900 mb-2">No trips found</h3>
                    <p className="text-secondary-500">
                      {filters.search || filters.status || filters.assignment_id || filters.date_from
                        ? 'Try adjusting your search criteria' 
                        : 'Trips will appear here once assignments are started'
                      }
                    </p>
                  </div>
                </td>
              </tr>
            ) : (
              trips.map((trip) => (
                <tr key={trip.id} className="hover:bg-secondary-50 transition-colors duration-150">
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-2xl mr-2 sm:mr-3 flex-shrink-0">🚚</span>
                      <div>
                        <div className="text-sm font-medium text-secondary-900">
                          Trip #{getDisplayTripNumber(trip, trips)}
                        </div>
                        {trip.is_exception && (
                          <div className="text-xs text-danger-600 font-medium">
                            ⚠️ Exception
                          </div>
                        )}                        {/* Mobile: Show status here */}
                        <div className="md:hidden mt-2">
                          {getStatusBadge(trip.status, trip)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    <div className="text-sm text-secondary-900">
                      <div className="font-medium truncate">
                        {trip.truck_number} • {trip.license_plate}
                      </div>
                      <div className="text-secondary-500 truncate">
                        {trip.driver_name}
                      </div>
                      {/* Add workflow indicator */}
                      {trip.is_extended_trip && (
                        <div className="text-xs text-blue-600 font-medium mt-1">
                          {getWorkflowIndicator(trip)}
                        </div>
                      )}
                      {/* Mobile: Show progress and duration here */}
                      <div className="md:hidden mt-2 space-y-1">
                        <div className="flex items-center">
                          <div className="w-16 bg-secondary-200 rounded-full h-1.5 mr-2">
                            <div 
                              className="bg-primary-600 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${getTripProgress(trip)}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-secondary-600 font-medium">
                            {getTripProgress(trip)}%
                          </span>
                        </div>
                        <div className="text-xs text-secondary-500 space-y-0.5">
                          {(() => {
                            const durations = calculateTripDurations(trip);
                            return (
                              <>
                                <div className="font-medium">Total: {formatDuration(durations.total)}</div>
                                <div>Load: {formatDuration(durations.load)}</div>
                                <div>Forward: {formatDuration(durations.travel1)}</div>
                                <div>Unload: {formatDuration(durations.unload)}</div>
                                <div>Return: {formatDuration(durations.travel2)}</div>
                              </>
                            );
                          })()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4">
                    {renderEnhancedRoute(trip)}
                  </td>
                  <td className="hidden md:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(trip.status, trip)}
                  </td>
                  <td className="hidden md:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-secondary-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${getTripProgress(trip)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-secondary-600 font-medium">
                        {getTripProgress(trip)}%
                      </span>
                    </div>
                  </td>
                  <td className="hidden lg:table-cell px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">
                      {(() => {
                        const durations = calculateTripDurations(trip);
                        return (
                          <div className="space-y-0.5">
                            <div className="font-medium text-blue-700">
                              Total: {formatDuration(durations.total)}
                            </div>
                            <div className="text-xs text-secondary-600 space-y-0.5">
                              <div>Load: {formatDuration(durations.load)}</div>
                              <div>Forward: {formatDuration(durations.travel1)}</div>
                              <div>Unload: {formatDuration(durations.unload)}</div>
                              <div>Return: {formatDuration(durations.travel2)}</div>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-900">
                      {formatDateTime(trip.loading_start_time || trip.created_at)}
                    </div>
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => onViewTrip(trip)}
                      className="text-primary-600 hover:text-primary-900 transition-colors p-1 rounded-md hover:bg-primary-50"
                      title="View Trip Details"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <Pagination />
    </div>
  );
};

export default TripsTable;