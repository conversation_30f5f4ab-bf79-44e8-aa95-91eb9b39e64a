# SSL/HTTPS Deployment Guide for Hauling QR Trip System

This guide provides step-by-step instructions for deploying the Hauling QR Trip System with SSL/HTTPS configuration for production environments.

## Overview

The system has been configured with comprehensive SSL/HTTPS support to enable:
- Secure camera access on mobile devices
- Encrypted data transmission
- WebSocket Secure (WSS) connections
- Production-ready security hardening

## Quick Start (Development)

### 1. Generate Development Certificates
```bash
cd server
node ssl/generate-dev-certs.js
```

### 2. Configure Environment
```bash
# Copy and edit environment file
cp .env.template .env

# Update SSL configuration
ENABLE_HTTPS=true
SSL_CERT_PATH=./ssl/dev/server.crt
SSL_KEY_PATH=./ssl/dev/server.key
FRONTEND_URL=https://**************:3000
```

### 3. Start Servers
```bash
# Backend (from server directory)
npm start

# Frontend (from client directory)
npm start
```

### 4. Test Configuration
```bash
# Run automated tests
node server/scripts/test-https.js

# Manual verification
curl -k https://**************:5443/health
```

## Production Deployment

### Phase 1: Certificate Acquisition

#### Option A: Let's Encrypt (Recommended)
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem server/ssl/production/server.key
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem server/ssl/production/fullchain.crt

# Set permissions
sudo chown app:app server/ssl/production/*
sudo chmod 600 server/ssl/production/server.key
sudo chmod 644 server/ssl/production/fullchain.crt
```

#### Option B: Commercial Certificate Authority
```bash
# Generate private key
openssl genrsa -out server/ssl/production/server.key 2048

# Generate certificate signing request
openssl req -new -key server/ssl/production/server.key -out server.csr

# Submit CSR to CA and download certificates
# Place certificates in server/ssl/production/
```

#### Option C: Corporate/Internal CA
```bash
# Follow your organization's certificate request process
# Ensure certificates include all required domains/IPs
# Place certificates in server/ssl/production/
```

### Phase 2: Environment Configuration

#### Production Environment Variables
```bash
# server/.env.production
NODE_ENV=production
ENABLE_HTTPS=true
PORT=80
HTTPS_PORT=443

# SSL Configuration
SSL_CERT_PATH=./ssl/production/fullchain.crt
SSL_KEY_PATH=./ssl/production/server.key
SSL_CA_PATH=./ssl/production/intermediate.crt

# Domain Configuration
FRONTEND_URL=https://yourdomain.com
FRONTEND_URL_HTTP=http://yourdomain.com

# Security
JWT_SECRET=your-production-jwt-secret
SESSION_SECRET=your-production-session-secret
```

#### Frontend Environment Variables
```bash
# client/.env.production
REACT_APP_API_URL=https://yourdomain.com/api
REACT_APP_WS_URL=wss://yourdomain.com
REACT_APP_USE_HTTPS=true
```

### Phase 3: Server Configuration

#### Reverse Proxy (Nginx) - Recommended
```nginx
# /etc/nginx/sites-available/hauling-qr
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/ssl/fullchain.crt;
    ssl_certificate_key /path/to/ssl/server.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    # Frontend
    location / {
        root /var/www/hauling-qr/build;
        try_files $uri $uri/ /index.html;
    }

    # API
    location /api {
        proxy_pass https://localhost:5443;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket
    location /ws {
        proxy_pass https://localhost:5443;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

#### Direct Node.js Deployment
```bash
# Start with production environment
NODE_ENV=production npm start

# Or with PM2
pm2 start ecosystem.config.js --env production
```

### Phase 4: Security Hardening

#### Firewall Configuration
```bash
# Allow HTTPS traffic
sudo ufw allow 443/tcp
sudo ufw allow 80/tcp

# Block direct access to Node.js ports (if using reverse proxy)
sudo ufw deny 5000/tcp
sudo ufw deny 5443/tcp
```

#### Certificate Permissions
```bash
# Set secure permissions
chmod 600 server/ssl/production/server.key
chmod 644 server/ssl/production/*.crt
chown app:app server/ssl/production/*
```

#### Monitoring Setup
```bash
# Certificate expiration monitoring
echo "0 2 * * * /usr/bin/openssl x509 -checkend 2592000 -noout -in /path/to/cert.crt || echo 'Certificate expires soon'" | crontab -
```

### Phase 5: Testing and Validation

#### Automated Testing
```bash
# Run comprehensive tests
NODE_ENV=production node server/scripts/test-https.js

# SSL Labs test (external)
# Visit: https://www.ssllabs.com/ssltest/analyze.html?d=yourdomain.com
```

#### Manual Verification
```bash
# Test HTTPS endpoints
curl https://yourdomain.com/health
curl https://yourdomain.com/api/health

# Test WebSocket
wscat -c wss://yourdomain.com

# Test certificate chain
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

#### Mobile Device Testing
1. Access `https://yourdomain.com` from mobile devices
2. Verify no certificate warnings
3. Test QR scanner camera access
4. Validate Trip Flow Logic functionality

## Certificate Management

### Automatic Renewal (Let's Encrypt)
```bash
# Add to crontab
0 12 * * * /usr/bin/certbot renew --quiet --post-hook "systemctl reload nginx"
```

### Manual Renewal Process
1. Generate new certificates
2. Update certificate files
3. Restart services
4. Verify functionality

### Certificate Monitoring
```bash
# Check expiration
openssl x509 -in server/ssl/production/fullchain.crt -noout -dates

# Automated monitoring script
node server/scripts/check-cert-expiry.js
```

## Troubleshooting

### Common Issues

#### Certificate Not Trusted
- Verify certificate chain is complete
- Check intermediate certificates are included
- Ensure certificate matches domain name

#### Camera Access Denied
- Confirm HTTPS is being used
- Verify no mixed content warnings
- Check browser permissions

#### WebSocket Connection Failed
- Verify WSS URL configuration
- Check firewall rules
- Confirm certificate covers WebSocket endpoint

#### Performance Issues
- Enable HTTP/2
- Configure proper caching headers
- Optimize SSL cipher suites

### Debug Commands
```bash
# Test SSL configuration
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Check certificate details
openssl x509 -in certificate.crt -text -noout

# Verify certificate chain
openssl verify -CAfile intermediate.crt certificate.crt

# Test WebSocket
wscat -c wss://yourdomain.com --no-check
```

## Monitoring and Maintenance

### Health Checks
- SSL certificate expiration
- HTTPS endpoint availability
- WebSocket connectivity
- Mobile device compatibility

### Log Monitoring
```bash
# Application logs
tail -f server/logs/app.log

# Nginx logs (if using reverse proxy)
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### Performance Monitoring
- Response time metrics
- SSL handshake performance
- WebSocket connection stability
- Mobile device performance

## Backup and Recovery

### Certificate Backup
```bash
# Backup certificates
tar -czf ssl-backup-$(date +%Y%m%d).tar.gz server/ssl/production/
```

### Recovery Procedures
1. Restore certificate files
2. Update environment configuration
3. Restart services
4. Verify functionality

## Support and Documentation

### Additional Resources
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [SSL Labs Server Test](https://www.ssllabs.com/ssltest/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)

### Contact Information
- Development Team: [contact information]
- Operations Team: [contact information]
- Security Team: [contact information]

---

**Last Updated**: [Date]  
**Version**: 1.0  
**Reviewed By**: [Name]
