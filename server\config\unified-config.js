/**
 * Unified Configuration Module for Server
 * Loads configuration from the root .env file and applies environment-specific settings
 */

const path = require('path');
const fs = require('fs');

// Load configuration from root .env file
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Import the config loader from root
const { loadConfig } = require('../../config-loader');

/**
 * Get unified configuration for server
 */
function getServerConfig() {
  const config = loadConfig();
  
  // Add server-specific derived values
  config.IS_DEVELOPMENT = config.NODE_ENV === 'development';
  config.IS_PRODUCTION = config.NODE_ENV === 'production';
  
  // CORS configuration - COMPREHENSIVE FIX for all access patterns
  config.CORS_ORIGINS = [
    // Environment-based URLs
    config.FRONTEND_URL,
    config.FRONTEND_URL_HTTP,
    config.FRONTEND_URL_HTTPS,

    // Localhost variations (all ports)
    `http://localhost:${config.CLIENT_PORT}`,
    `https://localhost:${config.CLIENT_PORT}`,
    'http://localhost:3000',
    'https://localhost:3000',
    'http://localhost:5000',
    'https://localhost:5000',

    // 127.0.0.1 variations
    `http://127.0.0.1:${config.CLIENT_PORT}`,
    `https://127.0.0.1:${config.CLIENT_PORT}`,
    'http://127.0.0.1:3000',
    'https://127.0.0.1:3000',
    'http://127.0.0.1:5000',
    'https://127.0.0.1:5000',

    // IP-based origins for mobile/network access
    `http://${config.IP_ADDRESS}:${config.CLIENT_PORT}`,
    `https://${config.IP_ADDRESS}:${config.CLIENT_PORT}`,
    `http://${config.IP_ADDRESS}:3000`,
    `https://${config.IP_ADDRESS}:3000`,
    `http://${config.IP_ADDRESS}:5000`,
    `https://${config.IP_ADDRESS}:5000`,

    // Common development patterns
    'http://0.0.0.0:3000',
    'https://0.0.0.0:3000'
  ];

  // CRITICAL: In development, allow ALL origins to prevent any CORS issues
  if (config.IS_DEVELOPMENT) {
    config.CORS_ORIGINS.push('*');
    console.log('🌐 DEVELOPMENT MODE: CORS set to allow ALL origins');
  }

  // Remove duplicates and null values
  config.CORS_ORIGINS = [...new Set(config.CORS_ORIGINS.filter(Boolean))];
  
  // SSL configuration paths (absolute paths)
  if (config.SSL_CERT_PATH) {
    // The SSL paths in .env are relative to server directory
    config.SSL_CERT_PATH_ABS = path.resolve(__dirname, '..', config.SSL_CERT_PATH);
    config.SSL_KEY_PATH_ABS = path.resolve(__dirname, '..', config.SSL_KEY_PATH);
    if (config.SSL_CA_PATH) {
      config.SSL_CA_PATH_ABS = path.resolve(__dirname, '..', config.SSL_CA_PATH);
    }
  }
  
  // Validate SSL certificates if HTTPS is enabled
  if (config.ENABLE_HTTPS) {
    if (!config.SSL_CERT_PATH_ABS || !fs.existsSync(config.SSL_CERT_PATH_ABS)) {
      console.warn(`⚠️  SSL certificate not found: ${config.SSL_CERT_PATH_ABS}`);
      config.SSL_AVAILABLE = false;
    } else if (!config.SSL_KEY_PATH_ABS || !fs.existsSync(config.SSL_KEY_PATH_ABS)) {
      console.warn(`⚠️  SSL key not found: ${config.SSL_KEY_PATH_ABS}`);
      config.SSL_AVAILABLE = false;
    } else {
      config.SSL_AVAILABLE = true;
    }
  } else {
    config.SSL_AVAILABLE = false;
  }
  
  // Rate limiting configuration - CRITICAL FIX for development
  config.RATE_LIMIT_CONFIG = {
    windowMs: config.IS_DEVELOPMENT ? 60000 : config.RATE_LIMIT_WINDOW_MS, // 1 minute in dev
    max: config.IS_DEVELOPMENT ? 1000 : config.RATE_LIMIT_MAX_REQUESTS, // Much higher limit in dev
    message: {
      error: config.IS_DEVELOPMENT ? 'Too Many HTTPS Requests' : 'Too many requests from this IP, please try again later.',
      message: config.IS_DEVELOPMENT ? 'Too many HTTPS requests from this IP, please try again later.' : 'Rate limit exceeded',
      retryAfter: config.IS_DEVELOPMENT ? '15 minutes' : Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: () => config.IS_DEVELOPMENT && config.DEV_DISABLE_RATE_LIMITING
  };
  
  config.AUTH_RATE_LIMIT_CONFIG = {
    windowMs: config.IS_DEVELOPMENT ? 60000 : config.AUTH_RATE_LIMIT_WINDOW_MS, // 1 minute in dev
    max: config.IS_DEVELOPMENT ? 100 : config.AUTH_RATE_LIMIT_MAX_REQUESTS, // Higher limit in dev
    message: {
      error: 'Too many authentication attempts from this IP, please try again later.',
      retryAfter: config.IS_DEVELOPMENT ? '1 minute' : Math.ceil(config.AUTH_RATE_LIMIT_WINDOW_MS / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: () => config.IS_DEVELOPMENT && config.DEV_DISABLE_RATE_LIMITING,
    skipSuccessfulRequests: true // Don't count successful requests
  };
  
  // Database configuration with SSL for production
  config.DB_CONFIG = {
    host: config.DB_HOST,
    port: config.DB_PORT,
    database: config.DB_NAME,
    user: config.DB_USER,
    password: config.DB_PASSWORD,
    max: config.DB_POOL_MAX,
    min: config.DB_POOL_MIN,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 60000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
    statement_timeout: 30000,
    query_timeout: 25000,
    application_name: 'hauling-qr-system',
    ssl: config.IS_PRODUCTION ? { rejectUnauthorized: false } : false
  };
  
  // Logging configuration
  config.LOG_CONFIG = {
    level: config.LOG_LEVEL,
    enableDetailedLogs: config.IS_DEVELOPMENT && config.DEV_ENABLE_DETAILED_LOGS,
    enableConsole: true,
    enableFile: config.IS_PRODUCTION
  };
  
  return config;
}

/**
 * Get SSL options for HTTPS server
 */
function getSSLOptions(config) {
  if (!config.ENABLE_HTTPS || !config.SSL_AVAILABLE) {
    return null;
  }
  
  const options = {
    cert: fs.readFileSync(config.SSL_CERT_PATH_ABS),
    key: fs.readFileSync(config.SSL_KEY_PATH_ABS)
  };
  
  if (config.SSL_CA_PATH_ABS && fs.existsSync(config.SSL_CA_PATH_ABS)) {
    options.ca = fs.readFileSync(config.SSL_CA_PATH_ABS);
  }
  
  return options;
}

/**
 * Display server configuration summary
 */
function displayServerConfig(config) {
  console.log('\n🔧 SERVER CONFIGURATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 HTTPS: ${config.ENABLE_HTTPS ? 'Enabled' : 'Disabled'}`);
  console.log(`🌐 IP Address: ${config.IP_ADDRESS}`);
  console.log(`🚀 HTTP Port: ${config.PORT}`);
  if (config.ENABLE_HTTPS) {
    console.log(`🔐 HTTPS Port: ${config.HTTPS_PORT}`);
    console.log(`📜 SSL Cert: ${config.SSL_AVAILABLE ? '✅ Available' : '❌ Missing'}`);
  }
  console.log(`🗄️  Database: ${config.DB_HOST}:${config.DB_PORT}/${config.DB_NAME}`);
  console.log(`⚡ Rate Limiting: ${config.RATE_LIMIT_CONFIG.skip ? 'Disabled (Dev)' : 'Enabled'}`);
  console.log(`📊 Logging: ${config.LOG_CONFIG.level} (${config.LOG_CONFIG.enableDetailedLogs ? 'Detailed' : 'Standard'})`);
  console.log('='.repeat(50));
}

module.exports = {
  getServerConfig,
  getSSLOptions,
  displayServerConfig
};
