/**
 * A→B→C Workflow Test Script
 * Tests the complete A→B→C workflow with the fixes applied
 */

const { getClient } = require('./server/config/database');

async function testABCWorkflow() {
  console.log('🧪 Testing A→B→C Workflow...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Verify test data exists
    console.log('📋 Step 1: Verifying test data...');
    
    const truckResult = await client.query('SELECT * FROM dump_trucks WHERE truck_number = $1', ['DT-100']);
    if (truckResult.rows.length === 0) {
      throw new Error('Test truck DT-100 not found');
    }
    const truck = truckResult.rows[0];
    console.log(`✅ Found truck: ${truck.truck_number}`);
    
    const locationsResult = await client.query('SELECT * FROM locations ORDER BY id');
    console.log(`✅ Found ${locationsResult.rows.length} locations:`);
    locationsResult.rows.forEach(loc => {
      console.log(`   - ${loc.name} (${loc.type})`);
    });
    
    // Step 2: Check for existing assignments
    console.log('\n📋 Step 2: Checking existing assignments...');
    
    const assignmentsResult = await client.query(`
      SELECT a.*, ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1 AND a.status = 'assigned'
      ORDER BY a.created_at DESC
    `, [truck.id]);
    
    console.log(`✅ Found ${assignmentsResult.rows.length} active assignments:`);
    assignmentsResult.rows.forEach(assignment => {
      console.log(`   - ${assignment.assignment_code}: ${assignment.loading_location} → ${assignment.unloading_location}`);
    });
    
    // Step 3: Check for recent trips
    console.log('\n📋 Step 3: Checking recent trips...');
    
    const tripsResult = await client.query(`
      SELECT tl.*, a.assignment_code, ll.name as loading_location, ul.name as unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
      ORDER BY tl.created_at DESC
      LIMIT 5
    `, [truck.id]);
    
    console.log(`✅ Found ${tripsResult.rows.length} recent trips:`);
    tripsResult.rows.forEach(trip => {
      console.log(`   - Trip #${trip.trip_number}: ${trip.loading_location} → ${trip.unloading_location} (${trip.status})`);
    });
    
    // Step 4: Test AutoAssignmentCreator with independentAssignment=true
    console.log('\n📋 Step 4: Testing AutoAssignmentCreator...');
    
    const { AutoAssignmentCreator } = require('./server/utils/AutoAssignmentCreator');
    const autoAssignmentCreator = new AutoAssignmentCreator();
    
    // Get a loading location for testing
    const loadingLocationResult = await client.query('SELECT * FROM locations WHERE type = $1 LIMIT 1', ['loading']);
    if (loadingLocationResult.rows.length === 0) {
      throw new Error('No loading locations found for testing');
    }
    const testLocation = loadingLocationResult.rows[0];
    
    console.log(`🔧 Testing auto-assignment creation at ${testLocation.name}...`);
    
    try {
      const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
        truck: truck,
        location: testLocation,
        client: client,
        userId: 1,
        enableDynamicRouting: true,
        independentAssignment: true
      });
      
      if (autoAssignmentResult && autoAssignmentResult.success) {
        console.log(`✅ Auto-assignment created successfully:`);
        console.log(`   - Assignment ID: ${autoAssignmentResult.assignment.id}`);
        console.log(`   - Assignment Code: ${autoAssignmentResult.assignment.assignment_code}`);
        console.log(`   - Loading Location ID: ${autoAssignmentResult.assignment.loading_location_id}`);
        console.log(`   - Unloading Location ID: ${autoAssignmentResult.assignment.unloading_location_id}`);
      } else {
        console.log(`❌ Auto-assignment creation failed: ${autoAssignmentResult?.error || 'Unknown error'}`);
      }
    } catch (autoAssignmentError) {
      console.log(`❌ Auto-assignment creation error: ${autoAssignmentError.message}`);
    }
    
    // Step 5: Test database constraints
    console.log('\n📋 Step 5: Testing database constraints...');
    
    try {
      await client.query('BEGIN');
      
      const testAssignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id, loading_location_id, 
          unloading_location_id, status, assigned_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *
      `, [
        'TEST-' + Date.now(),
        truck.id,
        1, // Assuming driver ID 1 exists
        testLocation.id,
        testLocation.id, // Use same location as placeholder
        'assigned',
        new Date().toISOString().split('T')[0]
      ]);
      
      console.log(`✅ Test assignment created with ID: ${testAssignmentResult.rows[0].id}`);
      
      await client.query('ROLLBACK'); // Clean up test data
      console.log(`✅ Test assignment cleaned up`);
      
    } catch (constraintError) {
      await client.query('ROLLBACK');
      console.log(`❌ Database constraint test failed: ${constraintError.message}`);
    }
    
    console.log('\n🎉 A→B→C Workflow test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testABCWorkflow().catch(console.error);
}

module.exports = { testABCWorkflow };
