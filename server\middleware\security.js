/**
 * Security Middleware for Hauling QR Trip System
 * Implements comprehensive security hardening for HTTPS deployment
 */

const fs = require('fs');
const path = require('path');

/**
 * SSL Certificate Monitoring
 * Checks certificate expiration and logs warnings
 */
function checkCertificateExpiration() {
  try {
    // Use unified config SSL paths
    const { getServerConfig } = require('../config/unified-config');
    const config = getServerConfig();

    if (!config.SSL_CERT_PATH_ABS) return;

    const certPath = config.SSL_CERT_PATH_ABS;
    if (!fs.existsSync(certPath)) return;
    
    const { execSync } = require('child_process');
    const certInfo = execSync(`openssl x509 -in "${certPath}" -noout -dates`, { encoding: 'utf8' });
    
    const notAfterMatch = certInfo.match(/notAfter=(.+)/);
    if (notAfterMatch) {
      const expiryDate = new Date(notAfterMatch[1]);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
      
      const warningDays = parseInt(process.env.CERT_EXPIRY_WARNING_DAYS) || 30;
      
      if (daysUntilExpiry <= warningDays) {
        console.warn(`⚠️  SSL Certificate expires in ${daysUntilExpiry} days (${expiryDate.toDateString()})`);
        
        if (daysUntilExpiry <= 7) {
          console.error(`🚨 SSL Certificate expires in ${daysUntilExpiry} days! Renewal required urgently.`);
        }
      } else {
        console.log(`✅ SSL Certificate valid until ${expiryDate.toDateString()} (${daysUntilExpiry} days)`);
      }
    }
  } catch (error) {
    console.warn('Could not check SSL certificate expiration:', error.message);
  }
}

/**
 * Security Headers Middleware
 * Adds comprehensive security headers for HTTPS deployment
 */
function securityHeaders(req, res, next) {
  // Strict Transport Security (HSTS)
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  // Content Security Policy - Mobile-friendly
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Allow eval for mobile compatibility
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: blob:", // Allow blob for camera
    "connect-src 'self' ws: wss: https:",
    "font-src 'self' data:",
    "object-src 'none'",
    "media-src 'self' blob:", // Allow blob for camera/media
    "frame-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');
  
  res.setHeader('Content-Security-Policy', csp);
  
  // Additional security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Mobile-friendly permissions - Allow camera for QR scanning
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('Permissions-Policy', 'camera=(self), microphone=(), geolocation=()');
  } else {
    res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  }

  // Remove server information
  res.removeHeader('X-Powered-By');

  // Development: Add headers to help with self-signed certificates
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('Access-Control-Allow-Credentials', 'true');
  }
  
  next();
}

/**
 * Secure Cookie Configuration
 * Configures cookies for HTTPS deployment
 */
function configureCookies(app) {
  // Configure session cookies for HTTPS
  if (process.env.NODE_ENV === 'production') {
    app.set('trust proxy', 1); // Trust first proxy
  }
  
  // Cookie security settings
  const cookieConfig = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: 'strict',
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000 // 24 hours
  };
  
  return cookieConfig;
}

/**
 * HTTP to HTTPS Redirect Middleware
 * Redirects HTTP requests to HTTPS in production
 */
function httpsRedirect(req, res, next) {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    const httpsUrl = `https://${req.get('host')}${req.url}`;
    console.log(`Redirecting HTTP to HTTPS: ${req.url} -> ${httpsUrl}`);
    return res.redirect(301, httpsUrl);
  }
  next();
}

/**
 * SSL Certificate Validation Middleware
 * Validates SSL certificate configuration
 */
function validateSSLConfig() {
  if (process.env.ENABLE_HTTPS !== 'true') {
    console.warn('⚠️  HTTPS is disabled. Camera access may not work on mobile devices.');
    return;
  }

  // Use unified config SSL paths
  const { getServerConfig } = require('../config/unified-config');
  const config = getServerConfig();

  if (!config.SSL_CERT_PATH_ABS || !config.SSL_KEY_PATH_ABS) {
    console.warn('⚠️  SSL configuration paths not set in unified config.');
    return;
  }

  const keyPath = config.SSL_KEY_PATH_ABS;
  const certPath = config.SSL_CERT_PATH_ABS;
  
  if (!fs.existsSync(keyPath)) {
    console.error(`❌ SSL private key not found: ${keyPath}`);
    return;
  }
  
  if (!fs.existsSync(certPath)) {
    console.error(`❌ SSL certificate not found: ${certPath}`);
    return;
  }
  
  console.log('✅ SSL configuration validated');
  
  // Check certificate expiration
  checkCertificateExpiration();
  
  // Schedule periodic certificate checks (daily)
  setInterval(checkCertificateExpiration, 24 * 60 * 60 * 1000);
}

/**
 * Rate Limiting for SSL/HTTPS endpoints
 */
function createSSLRateLimit() {
  const rateLimit = require('express-rate-limit');
  
  return rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
      error: 'Too Many HTTPS Requests',
      message: 'Too many HTTPS requests from this IP, please try again later.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/api/health';
    }
  });
}

/**
 * Error Handler for SSL/HTTPS Issues
 */
function sslErrorHandler(err, req, res, next) {
  // Handle SSL-specific errors
  if (err.code === 'EPROTO' || err.code === 'ECONNRESET') {
    console.error('SSL Protocol Error:', err.message);
    return res.status(400).json({
      error: 'SSL Protocol Error',
      message: 'SSL connection failed. Please check your HTTPS configuration.',
      code: err.code
    });
  }
  
  // Handle certificate errors
  if (err.code === 'CERT_UNTRUSTED' || err.code === 'CERT_INVALID') {
    console.error('SSL Certificate Error:', err.message);
    return res.status(400).json({
      error: 'SSL Certificate Error',
      message: 'SSL certificate validation failed.',
      code: err.code
    });
  }
  
  // Pass other errors to default handler
  next(err);
}

/**
 * Initialize Security Middleware
 */
function initializeSecurity(app) {
  console.log('🔒 Initializing security middleware...');
  
  // Validate SSL configuration
  validateSSLConfig();
  
  // Apply security headers
  app.use(securityHeaders);
  
  // Apply HTTPS redirect (production only)
  if (process.env.NODE_ENV === 'production') {
    app.use(httpsRedirect);
  }
  
  // Apply SSL rate limiting
  app.use('/api', createSSLRateLimit());
  
  // Configure secure cookies
  const cookieConfig = configureCookies(app);
  
  console.log('✅ Security middleware initialized');
  
  return { cookieConfig };
}

module.exports = {
  initializeSecurity,
  securityHeaders,
  httpsRedirect,
  sslErrorHandler,
  validateSSLConfig,
  checkCertificateExpiration,
  configureCookies
};
