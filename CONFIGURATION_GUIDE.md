# 🔧 Configuration Guide - Hauling QR Trip System

## 📋 **Port Configuration - PERMANENT SETUP**

### **🔒 PERMANENT PORTS (Never Change These):**

| Service | Development | Production | Purpose |
|---------|-------------|------------|---------|
| **Frontend (React)** | `3000` | `3000` | Client application |
| **Backend HTTP** | `5000` | `5000` | API server (fallback) |
| **Backend HTTPS** | `5444` | `5444` | Secure API server |

### **🌐 Access URLs:**

**Development Mode:**
- Frontend: `https://YOUR_IP:3000`
- Backend API: `https://YOUR_IP:5444/api`
- Health Check: `https://YOUR_IP:5444/health`

**Production Mode:**
- Frontend: `https://YOUR_DOMAIN:3000` or served from backend
- Backend API: `https://YOUR_DOMAIN:5444/api`
- Health Check: `https://YOUR_DOMAIN:5444/health`

---

## 📁 **Configuration Files Explained**

### **1. Active Configuration Files (USED BY SYSTEM):**

#### **`.env` (Root Level) - MAIN CONFIGURATION**
- **Location:** `/Hauling-QR-Trip-System/.env`
- **Purpose:** Primary configuration file used by the system
- **Status:** ✅ **ACTIVE** - This file controls the entire system
- **Contains:** Database, ports, SSL, CORS, all environment settings

#### **`client/.env.local` (Auto-Generated)**
- **Location:** `/client/.env.local`
- **Purpose:** React-specific environment variables
- **Status:** ✅ **ACTIVE** - Auto-generated by startup scripts
- **Contains:** Frontend-specific settings (API URLs, WebSocket, HTTPS)

### **2. Template Files (REFERENCE ONLY):**

#### **`server/.env.template` - SERVER TEMPLATE**
- **Location:** `/server/.env.template`
- **Purpose:** ⚠️ **REFERENCE ONLY** - Shows server configuration options
- **Status:** 📋 **TEMPLATE** - Not used by system, for documentation
- **Use Case:** Copy settings to main `.env` file when needed

#### **`client/.env.template` - CLIENT TEMPLATE**
- **Location:** `/client/.env.template`
- **Purpose:** ⚠️ **REFERENCE ONLY** - Shows client configuration options
- **Status:** 📋 **TEMPLATE** - Not used by system, for documentation
- **Use Case:** Reference for manual client configuration

#### **`server/.env.example` - BASIC EXAMPLE**
- **Location:** `/server/.env.example`
- **Purpose:** ⚠️ **LEGACY** - Basic example configuration
- **Status:** 📋 **LEGACY** - Outdated, use main `.env` instead

---

## 🚀 **How Configuration Works**

### **Startup Process:**
1. **System reads:** `/Hauling-QR-Trip-System/.env` (main config)
2. **Auto-detects:** Your network IP address
3. **Generates:** `client/.env.local` with correct settings
4. **Starts:** Both frontend (port 3000) and backend (port 5444)

### **Configuration Priority:**
1. **Main `.env` file** (highest priority)
2. **Environment variables** (if set)
3. **Default values** (fallback)

---

## ⚙️ **Available Commands**

### **Development Commands:**
```bash
npm run dev        # HTTP mode (ports: 3000, 5000)
npm run dev:https  # HTTPS mode (ports: 3000, 5444) ✅ RECOMMENDED
```

### **Production Commands:**
```bash
npm run prod       # HTTP production (ports: 3000, 5000)
npm run prod:https # HTTPS production (ports: 3000, 5444) ✅ RECOMMENDED
```

---

## 🔧 **Configuration Customization**

### **To Change Settings:**
1. **Edit:** `/Hauling-QR-Trip-System/.env` (main file)
2. **Restart:** `npm run dev:https` or your preferred command
3. **System automatically:** Updates client configuration

### **Common Customizations:**
```bash
# Database settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=hauling_qr_system

# Network settings
AUTO_DETECT_IP=true          # Automatic IP detection
MANUAL_IP=*************      # Manual IP override

# SSL settings
ENABLE_HTTPS=true            # Enable HTTPS
SSL_CERT_PATH_DEV=./ssl/dev/server.crt
```

---

## ❗ **Important Notes**

### **DO NOT:**
- ❌ Edit template files (`.env.template`) - they're for reference only
- ❌ Change permanent ports (3000, 5444) - system depends on these
- ❌ Edit `client/.env.local` manually - it's auto-generated

### **DO:**
- ✅ Edit main `.env` file for all configuration changes
- ✅ Use `npm run dev:https` for development with HTTPS
- ✅ Keep ports 3000 (frontend) and 5444 (backend HTTPS) permanent
- ✅ Use auto IP detection for network access

---

## 🆘 **Troubleshooting**

### **Frontend showing port 5000 instead of 3000:**
- **Cause:** Configuration mismatch
- **Fix:** Restart with `npm run dev:https`

### **"Network error" on login:**
- **Cause:** Backend not accessible
- **Fix:** Check backend is running on port 5444

### **Template file confusion:**
- **Remember:** Only `.env` (root) is used by system
- **Templates:** Are for reference and documentation only
