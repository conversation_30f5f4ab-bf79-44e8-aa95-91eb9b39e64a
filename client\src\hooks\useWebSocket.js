import { useEffect, useRef, useState, useCallback } from 'react';
import toast from 'react-hot-toast';

const useWebSocket = (user = null) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const ws = useRef(null);
  const reconnectTimeout = useRef(null);
  const heartbeatInterval = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Get WebSocket URL with enhanced protocol detection
  const getWebSocketUrl = useCallback(() => {
    // Import here to avoid issues with SSR
    const { getWebSocketUrl: getWebSocketUrlUtil } = require('../utils/network-utils');
    return getWebSocketUrlUtil();
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!user) return;

    try {
      const wsUrl = getWebSocketUrl();
      console.log('Connecting to WebSocket:', wsUrl);
      
      ws.current = new WebSocket(wsUrl);
      setConnectionStatus('connecting');

      ws.current.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;

        // Authenticate with server
        ws.current.send(JSON.stringify({
          type: 'auth',
          userId: user.id,
          role: user.role,
          timestamp: new Date().toISOString()
        }));

        // CRITICAL FIX: Start heartbeat to keep connection alive
        if (heartbeatInterval.current) {
          clearInterval(heartbeatInterval.current);
        }

        heartbeatInterval.current = setInterval(() => {
          if (ws.current && ws.current.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({
              type: 'ping',
              timestamp: new Date().toISOString()
            }));
          }
        }, 30000); // Send ping every 30 seconds
      };

      ws.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('WebSocket message received:', message);
          
          setLastMessage(message);

          // Handle different message types
          switch (message.type) {
            case 'auth_success':
              console.log('WebSocket authentication successful');
              break;

            case 'exception_created':
              // Show notification for new exceptions
              if (user.role === 'admin' || user.role === 'supervisor') {
                toast((t) => (
                  <div className="max-w-md">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <span className="text-red-600 text-sm">⚠️</span>
                        </div>
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="text-sm font-medium text-red-900">
                          {message.title}
                        </div>
                        <div className="text-sm text-red-700 mt-1">
                          {message.message}
                        </div>
                        <div className="text-xs text-red-600 mt-2">
                          Priority: {message.priority?.toUpperCase() || 'MEDIUM'}
                        </div>
                      </div>
                      <button
                        onClick={() => toast.dismiss(t.id)}
                        className="ml-3 text-red-400 hover:text-red-600"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                ), {
                  duration: 10000,
                  position: 'top-right',
                  style: {
                    background: '#fef2f2',
                    border: '1px solid #ef4444',
                    borderRadius: '8px',
                    padding: '16px'
                  }
                });
              }
              
              // Add to notifications list
              setNotifications(prev => [message, ...prev.slice(0, 19)]); // Keep last 20
              break;

            case 'exception_updated':
              // Show notification for exception updates
              const isApproved = message.decision === 'approved';
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isApproved ? 'bg-green-100' : 'bg-red-100'
                      }`}>
                        <span className={`text-sm ${
                          isApproved ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {isApproved ? '✅' : '❌'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className={`text-sm font-medium ${
                        isApproved ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {message.title}
                      </div>
                      <div className={`text-sm mt-1 ${
                        isApproved ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className={`ml-3 hover:opacity-75 ${
                        isApproved ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 8000,
                position: 'top-right',
                style: {
                  background: isApproved ? '#f0fdf4' : '#fef2f2',
                  border: `1px solid ${isApproved ? '#22c55e' : '#ef4444'}`,
                  borderRadius: '8px',
                  padding: '16px'
                }
              });

              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'trip_status_changed':
              // Show subtle notification for trip updates
              toast.success(message.message, { duration: 3000 });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'route_discovery_started':
              // Show notification for route discovery start
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 text-sm">🔄</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-blue-900">
                        {message.title}
                      </div>
                      <div className="text-sm mt-1 text-blue-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-blue-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 4000,
                position: 'top-right',
                style: {
                  background: '#eff6ff',
                  border: '1px solid #3b82f6',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'route_location_confirmed':
              // Show notification for location confirmation
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">{message.icon || '📍'}</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-green-900">
                        {message.title}
                      </div>
                      <div className="text-sm mt-1 text-green-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-green-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 5000,
                position: 'top-right',
                style: {
                  background: '#f0fdf4',
                  border: '1px solid #22c55e',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'route_updated':
              // Show notification for route updates
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                        <span className="text-amber-600 text-sm">🔄</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-amber-900">
                        {message.title}
                      </div>
                      <div className="text-sm mt-1 text-amber-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-amber-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 6000,
                position: 'top-right',
                style: {
                  background: '#fffbeb',
                  border: '1px solid #f59e0b',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'route_discovery_completed':
              // Show notification for completed route discovery
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span className="text-indigo-600 text-sm">✅</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-indigo-900">
                        {message.title}
                      </div>
                      <div className="text-sm mt-1 text-indigo-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-indigo-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 5000,
                position: 'top-right',
                style: {
                  background: '#f0f9ff',
                  border: '1px solid #6366f1',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'trip_extended':
              // Show notification for extended trip
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 text-sm">🔄</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-blue-900">
                        Extended Trip Started
                      </div>
                      <div className="text-sm mt-1 text-blue-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-blue-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 4000,
                position: 'top-right',
                style: {
                  background: '#eff6ff',
                  border: '1px solid #3b82f6',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'cycle_started':
              // Show notification for cycle trip
              toast((t) => (
                <div className="max-w-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">🔄</span>
                      </div>
                    </div>
                    <div className="ml-3 flex-1">
                      <div className="text-sm font-medium text-green-900">
                        Cycle Trip Started
                      </div>
                      <div className="text-sm mt-1 text-green-700">
                        {message.message}
                      </div>
                    </div>
                    <button
                      onClick={() => toast.dismiss(t.id)}
                      className="ml-3 hover:opacity-75 text-green-400"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              ), {
                duration: 4000,
                position: 'top-right',
                style: {
                  background: '#f0fdf4',
                  border: '1px solid #22c55e',
                  borderRadius: '8px',
                  padding: '16px'
                }
              });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'dynamic_route':
              // Show notification for dynamic route discovery
              toast.success(message.message, { duration: 3000 });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            case 'workflow_completed':
              // Show subtle notification for workflow completion
              toast.success(message.message, { duration: 2000 });
              setNotifications(prev => [message, ...prev.slice(0, 19)]);
              break;

            default:
              console.log('Unknown WebSocket message type:', message.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');

        // CRITICAL FIX: More aggressive reconnection for mobile browsers
        // Don't reconnect if intentionally closed (code 1000) or if user logged out
        if (event.code !== 1000 && user && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000); // Reduced max delay
          console.log(`🔄 WebSocket reconnecting in ${delay}ms... (attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts})`);

          setConnectionStatus('reconnecting');

          reconnectTimeout.current = setTimeout(() => {
            reconnectAttempts.current++;
            console.log(`🔌 Attempting WebSocket reconnection #${reconnectAttempts.current}`);
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          console.log('❌ Max WebSocket reconnection attempts reached');
          setConnectionStatus('failed');
          // Don't show error toast immediately - user might be navigating
          setTimeout(() => {
            if (user) { // Only show if user is still logged in
              toast.error('Connection lost. Please refresh the page.', { duration: 5000 });
            }
          }, 2000);
        }
      };

      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');

        // Enhanced error handling for SSL/HTTPS issues
        if (error.type === 'error' && wsUrl.startsWith('wss:')) {
          console.warn('WSS connection failed. This might be due to:');
          console.warn('1. Self-signed certificate not trusted');
          console.warn('2. SSL certificate mismatch');
          console.warn('3. HTTPS server not running');
          console.warn('Try accessing the HTTPS health check first:', wsUrl.replace('wss:', 'https:') + '/health');
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, [user, getWebSocketUrl]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
    }

    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current);
    }

    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.close(1000, 'User disconnected');
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, []);

  // Send message
  const sendMessage = useCallback((message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Mark notification as read
  const markNotificationAsRead = useCallback((notificationId) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
  }, []);

  // Connect when user is available
  useEffect(() => {
    if (user) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [user, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current);
      }
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionStatus,
    lastMessage,
    notifications,
    unreadCount: notifications.filter(n => !n.read).length,
    sendMessage,
    clearNotifications,
    markNotificationAsRead,
    reconnect: connect
  };
};

export default useWebSocket;
