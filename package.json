{"name": "hauling-qr-trip-system", "version": "1.0.0", "description": "QR Code-based Hauling Truck Trip Management System", "main": "server/server.js", "scripts": {"dev": "node scripts/start-dev.js", "dev:https": "node scripts/start-dev-https.js", "prod": "node scripts/start-prod.js", "prod:https": "node scripts/start-prod-https.js", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-deps": "npm install && cd server && npm install && cd ../client && npm install", "setup": "npm run install-deps && npm run setup-db", "setup-db": "psql -U postgres -d postgres -f database/init.sql", "config:dev": "node scripts/configure-env.js development false", "config:dev-https": "node scripts/configure-env.js development true", "config:prod": "node scripts/configure-env.js production false", "config:prod-https": "node scripts/configure-env.js production true"}, "keywords": ["qr-code", "hauling", "truck", "trip-management", "logistics"], "author": "Hauling Management System", "license": "MIT", "devDependencies": {"axios": "^1.10.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "dependencies": {"dotenv": "^16.3.1", "file-saver": "^2.0.5", "pg": "^8.16.1", "ws": "^8.18.2"}}