# Trip Flow Logic Implementation

## Overview

The Hauling QR Trip System implements a comprehensive Trip Flow Logic that handles standard cycles and dynamic route adaptations. This document outlines the complete implementation and business rules.

## Core Business Rules

### 1. Standard Cycle: A → B → A
- A dump truck follows an assigned trip flow: load at Point A, unload at Point B, return to Point A
- This creates the standard cycle: **A → B → A**
- Each completed cycle increments the trip counter

### 2. Unassigned Loading Location
**Business Rule**: Creates a **NEW trip row** and auto-creates assignment

**Implementation**:
- When truck scans at unassigned loading location (e.g., Point C)
- System automatically creates new trip row in Trip Monitoring Data Table
- Starts new trip with Point C as loading location
- Previous trip ends at its last unloading point
- Auto-creates new assignment for future standard flows

**Code Location**: `server/routes/scanner.js` - `handlePostCompletionLoading()` function

### 3. Unassigned Unloading Location
**Business Rule**: **Updates current trip row** and auto-creates assignment

**Implementation**:
- When truck scans at unassigned unloading location during `loading_end` state
- System does NOT create new trip row
- Updates current active trip by setting unloading location
- Trip is marked as completed with the new unloading location
- Auto-creates new assignment for future trips including this location

**Code Location**: `server/routes/scanner.js` - `handleLoadingEnd()` function (lines 1050-1165)

## Trip Display Format

### Frontend Display Rules
Each trip row in Trip Monitoring displays exactly two points:

```
⬆️ (Loading Location) → ⬇️ (Unloading Location)
```

### Status Indicators
- **📍** = Confirmed scans (truck physically visited)
- **❓** = Predicted/unscanned locations (expected based on history)

### Examples
- `📍 POINT A ⬆️ → 📍 POINT B ⬇️` (completed trip)
- `📍 POINT C ⬆️ → ❓ To be determined ⬇️` (independent assignment)
- `❓ POINT A ⬆️ → 📍 POINT D ⬇️` (dynamic route discovery)

## Implementation Details

### 1. Scanner Logic Flow

```javascript
// Main processing in processTruckScan()
1. Validate truck and location data
2. CRITICAL: Validate location type compatibility
3. Check for post-completion scenarios (A→B→C workflows)
4. Validate existing assignments with role matching
5. Handle active trip progression
6. Create auto-assignments when needed (with validation)
```

### 2. Location Type Validation

**CRITICAL REQUIREMENT**: All operations must validate location types to prevent invalid states.

#### Loading Operations
- **MUST** only occur at locations with `type='loading'`
- **REJECTED** at locations with `type='unloading'`
- Error: "Cannot start loading operation at unloading location"

#### Unloading Operations
- **MUST** only occur at locations with `type='unloading'`
- **REJECTED** at locations with `type='loading'`
- Error: "Cannot start unloading operation at loading location"

#### Assignment Role Validation
- Assignment `location_role='loading'` **MUST** match `location.type='loading'`
- Assignment `location_role='unloading'` **MUST** match `location.type='unloading'`
- Error: "Assignment role mismatch: Assignment expects [operation] but location is [type]"

### 3. Validation Implementation Points

#### Scanner.js Validation
1. **handleNewTrip()** - Lines 1063-1114
   - Validates location type before trip creation
   - Rejects invalid combinations with clear errors
   - Removed dangerous fallback logic

2. **Assignment Validation** - Lines 578-633
   - Validates assignment role matches location type
   - Prevents role/type mismatches
   - Clear error messages for debugging

#### AutoAssignmentCreator.js Validation
1. **shouldCreateAutoAssignment()** - Lines 212-224
   - Validates location type before creation
   - Rejects invalid location types

2. **createDynamicAssignment()** - Lines 309-323
   - Validates location type at start
   - Only allows 'loading' or 'unloading' types

#### Frontend Validation
1. **QRScanner.js** - Lines 371-399
   - Client-side location type validation
   - Enhanced error message display
   - User-friendly validation feedback

### 4. Auto-Assignment Creation

**For Unassigned Loading**:
```javascript
// In handlePostCompletionLoading()
const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
  truck,
  location,
  client,
  userId,
  enableDynamicRouting: true,
  independentAssignment: true // Creates C → null structure
});
```

**For Unassigned Unloading**:
```javascript
// In handleLoadingEnd()
const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
  truck,
  location,
  client,
  userId,
  enableDynamicRouting: true // Includes new unloading location
});
```

### 3. Trip Status Progression

Standard progression for all trips:
```
assigned → loading_start → loading_end → unloading_start → unloading_end → trip_completed
```

### 4. Database Schema

**Trip Logs Table**:
- `actual_loading_location_id` - Where truck actually loaded
- `actual_unloading_location_id` - Where truck actually unloaded
- `is_extended_trip` - Boolean for workflow tracking
- `workflow_type` - 'standard', 'extended', 'cycle', 'dynamic'

**Assignments Table**:
- `loading_location_id` - Assigned loading location
- `unloading_location_id` - Assigned unloading location (can be null for discovery)
- `notes` - JSON metadata including creation method

## Frontend Implementation

### Trip Monitoring Display
**File**: `client/src/pages/trips/components/TripsTable.js`

**Key Functions**:
- `renderDynamicRoute()` - Displays uncertainty indicators
- `getLocationCertainty()` - Determines 📍 vs ❓ status
- `getLocationDisplay()` - Formats location with proper icons

### Display Logic
```javascript
// Loading location certainty
const loadingCertainty = ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status) 
  ? 'confirmed' : 'predicted';

// Unloading location certainty  
const unloadingCertainty = ['unloading_start', 'unloading_end', 'trip_completed'].includes(trip.status)
  ? 'confirmed' : 'predicted';
```

## Testing

### Test Suite
**File**: `server/test-trip-flow-logic.js`

**Test Coverage**:
1. Standard Cycle (A → B → A)
2. Unassigned Loading Location Logic
3. Unassigned Unloading Location Logic
4. Trip Display Format Verification
5. Auto-Assignment Creation Validation

### Running Tests
```bash
node server/test-trip-flow-logic.js
```

## Key Benefits

1. **Accurate Trip Tracking**: Each trip row represents exactly one journey
2. **Dynamic Route Adaptation**: System adapts to real-world route changes
3. **Future Planning**: Auto-created assignments improve future efficiency
4. **Clear Visual Feedback**: Uncertainty indicators show data confidence
5. **Audit Trail**: Complete history of actual vs planned routes

## Summary

The Trip Flow Logic ensures:

✅ **Unassigned loading** → creates a new trip row and auto-creates assignment  
✅ **Unassigned unloading** → updates current trip row and also auto-creates assignment  
✅ **Trip display** → Always ⬆️ (Loading) → ⬇️ (Unloading) format  
✅ **Status indicators** → 📍 for confirmed, ❓ for predicted  
✅ **System accuracy** → Handles dynamic routes without breaking trip tracking logic

This approach ensures the system stays accurate, handles dynamic routes, and adapts over time while maintaining clear trip tracking logic.
